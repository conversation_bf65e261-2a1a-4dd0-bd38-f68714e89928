<?php

namespace DD\App\Mieszkania;

use DD\App\DTO\RealEstate;
use DD\App\Enum\Type;
use DD\App\Logger;
use DD\App\Mieszkania\PriceHistoryModal;
use DD\App\Enum\Status;
use DD\App\Repository\RealEstateRepository;
use WP_Term;

class Single {
    private int $post_id;
    private array $meta;
    private array $details;
    private string $placeholder_image;
    private ?string $vertical_shot_image = null;
    private ?string $angled_shot_image = null;
    /**
     * @var array<string> Facilities names
     */
    private array $facilities = [];
    private RealEstateRepository $realEstateRepository;
    private RealEstate $realEstate;

    public function __construct(int $post_id) {
        $this->realEstateRepository = new RealEstateRepository();
        
        $this->post_id = $post_id;
        $realEstate = $this->realEstateRepository->getRealEstateByPostId($this->post_id);
        
        if ($realEstate === null) {
            Logger::error('Real estate not found in database: ' . $this->post_id);
            echo 'Wystąpił błąd. Spróbuj odświeżyć stronę.';
            return;
        }
        
        $this->realEstate = $realEstate;
        $this->load_meta();
        $this->prepare_details();
        $this->prepare_images();

        $this->placeholder_image = match ($this->meta[DD_TYPE] ?? '') {
            DD_TYPE_MIESZKANIE => DD_PLACEHOLDER_FLAT,
            DD_TYPE_MIEJSCE_POSTOJOWE_GARAZ,
            DD_TYPE_MIEJSCE_POSTOJOWE_ZEWNATRZ => DD_PLACEHOLDER_PARKING,
            DD_TYPE_KOMORKA_LOKATORSKA => DD_PLACEHOLDER_STORAGE,
            DD_TYPE_LOKAL_USLUGOWY => DD_PLACEHOLDER_FLAT,
            default => DD_PLACEHOLDER_FLAT,
        };
    }

    private function load_meta(): void {
        $meta_keys = [
          DD_TYPE, DD_PRICE, DD_PRICE_PER_METER, DD_STATUS, DD_FLOOR, DD_SQUARE,
          DD_ROOMS, DD_PDF_LINK, DD_3D_LINK, DD_ID, DD_VERTICAL_SHOT, DD_ANGLED_SHOT
        ];
        
        foreach ($meta_keys as $key) {
            $this->meta[$key] = get_post_meta($this->post_id, $key, true);
        }

        $raw_facilities = wp_get_post_terms($this->post_id, DD_FACILITIES);
        $this->facilities = array_map(function (WP_Term $term): string {
           return $term->name;
        }, $raw_facilities);
   }

   private function prepare_details(): void {
        $this->details = [
            'Status' => $this->realEstate->status,
            'Cena' => $this->realEstate->price > 0 ? [
                'price' => number_format($this->realEstate->price, 0, ',', ' ') . ' zł',
                'price_per_meter'  => $this->realEstate->pricePerMeter > 0 ? number_format($this->realEstate->pricePerMeter, 0, ',', ' ') . ' zł / m²' : null,
            ] : null,
            'Metraż' => $this->realEstate->area > 0 ? $this->realEstate->area . ' m²' : null,
            'Liczba pokoi' => $this->realEstate->rooms ?: null,
            'Piętro' => $this->realEstate->floor == '0' ? 'Parter' : ($this->realEstate->floor ?? null),
            'Udogodnienia' => !empty($this->facilities) ? implode(', ', $this->facilities) : null,
        ];
    }

    private function prepare_images(): void {
        $this->vertical_shot_image = $this->meta[DD_VERTICAL_SHOT] ?: null;
        $this->angled_shot_image = $this->meta[DD_ANGLED_SHOT] ?: null;
    }

    public function render(): string {
        ob_start(); ?>
        <a class="button reverse back" href="<?php echo esc_url(home_url('mieszkania/parter')); ?>">
            <svg width="13" height="10" viewBox="0 0 13 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 5.21875H1.03125M1.03125 5.21875L5.25 1M1.03125 5.21875L5.25 9.4375" stroke="#181716"
                      stroke-linecap="round" stroke-linejoin="round" />
            </svg>
            Powrót
        </a>
        <div class="mieszkanie-single">
            <div class="left-right">
                <div class="left">
                    <h2 class="section-title mobile">
                        <?= esc_html($this->realEstate->type->displayName()) . ' <span class="highlight">' . esc_html($this->realEstate->localNumber) . '</span>' ?>
                    </h2>
                    <div class="main-image">
                        <?php if ($this->angled_shot_image !== null): ?>
                            <img src="<?= esc_url($this->angled_shot_image) ?>" alt="Zdjęcie mieszkania">
                        <?php else: ?>
                            <img src="<?= esc_url($this->placeholder_image) ?>" class="placeholder" alt="Zdjęcie mieszkania">
                        <?php endif; ?>
                        <?php if ($this->realEstate->type === Type::Mieszkanie && $this->angled_shot_image === null): ?>
                            <span class="placeholder-text">Rzut 3D mieszkania dostępny wkrótce</span>
                        <?php endif; ?>
                    </div>
                    <div class="gallery">
                        <div class="thumbnail">
                            <img src="<?= esc_url($this->angled_shot_image ?? $this->placeholder_image) ?>" alt="Zdjęcie mieszkania">
                            <span class="title">Widok ukośny</span>
                        </div>
                        <div class="thumbnail">
                            <img src="<?= esc_url($this->vertical_shot_image ?? $this->placeholder_image) ?>" alt="Zdjęcie mieszkania">
                            <span class="title">Widok z góry</span>
                        </div>
                        <?php if (!empty($this->meta[DD_3D_LINK])): ?>
                            <div
                                    class="walkaround"
                                    data-3d-link="<?= esc_url($this->meta[DD_3D_LINK]) ?>"
                            >
                                <img src="<?= esc_url(DD_PLUGIN_ASSETS . 'images/walkaround.webp') ?>" alt="Spacer 3D">
                                <span class="title">WIRTUALNY SPACER</span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="right">
                    <h2 class="section-title desktop">
                        <?= esc_html($this->realEstate->type->displayName()) . ' <span class="highlight">' . esc_html($this->realEstate->localNumber) . '</span>' ?>
                    </h2>
                    <div class="info">
                        <?php foreach ($this->details as $label => $value): ?>
                            <?php if (!$value) continue; ?>
                                <div class="row">
                                    <?php if ($label === 'Status'): ?>
                        <div class="wrapped">
                            <span class="status <?= esc_html($value->value) ?>"><?= esc_html($value->displayName()) ?></span>
                        </div>
                        <button class="dd_add-to-clipboard" id="<?= $this->post_id ?>" title="Dodaj do schowka">
                            <svg width="20" height="20" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M11.22 1.41606C10.8579 1.13727 10.4113 0.990877 9.95444 1.00125C9.49758 1.01162 9.05805 1.17813 8.709 1.47306L1.709 7.47206C1.4868 7.65986 1.30824 7.89387 1.18579 8.15778C1.06333 8.42169 0.99993 8.70913 1 9.00006V18.0001C1 18.5305 1.21071 19.0392 1.58579 19.4143C1.96086 19.7894 2.46957 20.0001 3 20.0001H17C17.5304 20.0001 18.0391 19.7894 18.4142 19.4143C18.7893 19.0392 19 18.5305 19 18.0001V10.6461M13 5.00006H19M16 2.00006V8.00006"
                                    stroke="#FFFFFF" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                        </button>

                       <?php elseif ($label === 'Cena' && is_array($value)): ?>
                        <div class="wrapped">
                            <strong><?= esc_html($label) ?></strong>
                            <span>(brutto)</span>
                        </div>
                        <div class="column">
                            <?php if (!empty($value['price'])): ?>
                                <strong class="price"><?= esc_html($value['price']) ?></strong>
                            <?php endif; ?>
                            <?php if (!empty($value['price_per_meter'])): ?>
                                <div class="price-per-m2"><?= esc_html($value['price_per_meter']) ?></div>
                            <?php endif; ?>
                        </div>
                        <?php else: ?>
                            <span><?= esc_html($label) ?></span><strong><?= esc_html($value) ?></strong>
                        <?php endif; ?>
                        </div>
                        <?php endforeach; ?>

                        <div class="wrapper">
                            <?php if (!empty($this->meta[DD_PDF_LINK])): ?>
                                <a class="link-pdf reverse" href="<?= esc_url($this->meta[DD_PDF_LINK]) ?>" target="_blank">Pobierz kartę
                                    lokalu<svg width="15" height="16" viewBox="0 0 15 16" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                        <path d="M7.5 11.5V1M7.5 11.5L1.5 5.5M7.5 11.5L13.5 5.5M14.5 15.5H0.5" stroke="black"
                                            stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                </a>
                            <?php endif; ?>
                            <button id="show-price-history" class="button reverse" type="button">Historia cen lokalu</button>
                            <?= PriceHistoryModal::renderPriceHistory($this->realEstate->priceHistory) ?>
                        </div>
                        <a class="link-pdf" href="<?php echo esc_url(home_url('kontakt')); ?>" target="_blank">Formularz kontaktowy</a>
                    </div>
                </div>
            </div>
        </div>

        <?php return ob_get_clean();
    }
}