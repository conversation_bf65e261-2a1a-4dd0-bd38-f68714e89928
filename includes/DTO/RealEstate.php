<?php

namespace DD\App\DTO;

use DD\App\Enum\Status;
use DD\App\Enum\Type;

class RealEstate
{
    public int $externalId;
    public string $localNumber;
    public int $floor;
    public Type $type;
    public Status $status;
    public float $price;
    public float $pricePerMeter;
    public ?string $cardLinkPdf = null;
    public bool $hasBalcony = false;
    public ?float $area = null;
    public ?int $rooms = null;
    public ?int $postId = null;
    /** @var array<PriceHistoryDto> */
    public array $priceHistory = [];

    public function __construct(
        int $externalId,
        string $localNumber,
        int $floor,
        Type $type,
        Status $status,
        float $price,
        float $pricePerMeter,
        bool $hasBalcony = false,
        ?string $cardLinkPdf = null,
        ?float $area = null,
        ?int $rooms = null,
        ?int $postId = null,
        array $priceHistory = []
    ) {
        $this->externalId = $externalId;
        $this->localNumber = $localNumber;
        $this->cardLinkPdf = $cardLinkPdf;
        $this->floor = $floor;
        $this->type = $type;
        $this->status = $status;
        $this->price = $price;
        $this->pricePerMeter = $pricePerMeter;
        $this->hasBalcony = $hasBalcony;
        $this->area = $area;
        $this->rooms = $rooms;
        $this->postId = $postId;
        $this->priceHistory = $priceHistory;
    }
}