<?php

use DD\App\Enum\Status;
use DD\App\Mieszkania\Single;

add_shortcode('dd_mieszkania_single', function () {
    $post_id = get_the_ID();
    if (!$post_id) {
        return '';
    }

    $mieszkanie = new Single($post_id);
    return $mieszkanie->render();
});

add_shortcode('dd_mieszkania_list', []);

add_shortcode('dd_featured_mieszkania', function () {
    $featured_properties = dd_get_featured_properties();
    $query = new WP_Query([
        'post_type'      => DD_POST_TYPE,
        'post__in'       => $featured_properties,
        'posts_per_page' => 4,
        'orderby'        => 'post__in',
    ]);

    ob_start();
    ?>
    <div class="swiper mieszkaniaSwiper">
        <div class="swiper-wrapper">
            <?php while ($query->have_posts()) : $query->the_post();
                $post_id = get_the_ID();
                $post_link = get_permalink($post_id);
                $id_number = get_post_meta($post_id, DD_ID, true);
                $price = get_post_meta($post_id, DD_PRICE, true);
                $pricePerMeter = get_post_meta($post_id, DD_PRICE_PER_METER, true);
                $square = get_post_meta($post_id, DD_SQUARE, true);
                $rooms = get_post_meta($post_id, DD_ROOMS, true);
                $floor = get_post_meta($post_id, DD_FLOOR, true);
                $images = get_post_meta($post_id, DD_ANGLED_SHOT, true);
                $image_url = !empty($images) ? esc_url($images) : esc_url(DD_PLACEHOLDER_FLAT);
                $status = Status::tryFrom(get_post_meta($post_id, DD_STATUS, true)) ?? Status::Sprzedane;

                $info_items = array_filter([
                    $square ? '<span class="square">' . esc_html($square) . ' m²</span>' : null,
                    $rooms ? '<span class="rooms">• ' . esc_html(dd_transform_rooms($rooms)) . '</span>' : null
                ]);
                
                if ($floor > -2) {
                    $floor_text = ($floor == 0) ? 'Parter' : ($floor == -1 ? 'Garaż' : esc_html($floor) . ' piętro');
                    $floor_item = $rooms > 0 
                        ? '<span class="floor">' . $floor_text . '</span>' 
                        : '<li class="floor"><span>' . $floor_text . '</span></li>';
                }
                ?>
                <div class="swiper-slide mieszkanie">
                    <a href="<?php echo esc_url($post_link); ?>" class="property-link">
                        <img src="<?php echo $image_url; ?>" alt="<?php the_title(); ?>" class="<?php echo empty($images) ? 'placeholder' : ''; ?>">
                        <h3 class="title"><?php echo esc_html($id_number); ?></h3>
                        <div class="content <?php echo empty($rooms) ? 'with-rooms' : ''; ?>">
                            <div class="top">
                                <?php if (!empty($info_items)): ?>
                                    <div class="info">
                                        <div>
                                            <?php echo implode('', $info_items); ?>
                                        </div>
                                        <?php if (!empty($floor_item)): ?>
                                        <div>
                                            <?php echo $floor_item; ?>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                                <p class="status <?php echo esc_attr($status->value); ?>">• <?php echo esc_html($status->displayName()); ?></p>
                            </div>
                            <?php if (!empty($price)): ?>
                                <div class="bottom">
                                    <div class="price">
                                        <span class="price-label">Cena:</span>
                                        <span class="price-value"><?php echo number_format($price, 0, ',', ' '); ?> zł</span>
                                        <?php if (!empty($pricePerMeter)): ?><span class="price-valuemkw"><?php echo number_format($pricePerMeter, 0, ',', ' '); ?> zł / m²</span> <?php endif; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </a>
                    <button class="dd_add-to-clipboard" id="<?php echo $post_id ?>" title="Dodaj do schowka">
                        <svg width="20" height="20" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M11.22 1.41606C10.8579 1.13727 10.4113 0.990877 9.95444 1.00125C9.49758 1.01162 9.05805 1.17813 8.709 1.47306L1.709 7.47206C1.4868 7.65986 1.30824 7.89387 1.18579 8.15778C1.06333 8.42169 0.99993 8.70913 1 9.00006V18.0001C1 18.5305 1.21071 19.0392 1.58579 19.4143C1.96086 19.7894 2.46957 20.0001 3 20.0001H17C17.5304 20.0001 18.0391 19.7894 18.4142 19.4143C18.7893 19.0392 19 18.5305 19 18.0001V10.6461M13 5.00006H19M16 2.00006V8.00006" stroke="#FFFFFF" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </button>
                </div>
            <?php endwhile; ?>
        </div>
        <div class="swiper-button-prev"></div>
        <div class="swiper-button-next"></div>
    </div>

    <?php
    wp_reset_postdata();
    return ob_get_clean();
});

function dd_transform_rooms($rooms) {
    if ($rooms == 1) {
        return "$rooms pokój";
    } elseif ($rooms >= 2 && $rooms <= 4) {
        return "$rooms pokoje";
    } else {
        return "$rooms pokoi";
    }
}

function dd_enqueue_swiper_script() {
    ?>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const mieszkaniaSwiper = new Swiper('.mieszkaniaSwiper', {
                loop: false,
                slidesPerView: 'auto',
                loopedSlides: 4,
                spaceBetween: 10,
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
                
            });
        });
    </script>
    <?php
}
add_action('wp_footer', 'dd_enqueue_swiper_script');

add_shortcode('dd_clipboard_count', function () {
    ob_start();
    ?>
    <a class="menu-item clipboard" href="<?php echo esc_url(home_url('schowek')); ?>">
        <div class="dd_clipboard-button">
            <svg width="21" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11.22 1.416C10.8579 1.13721 10.4113 0.990812 9.95444 1.00118C9.49758 1.01155 9.05805 1.17806 8.709 1.473L1.709 7.472C1.4868 7.6598 1.30824 7.89381 1.18579 8.15772C1.06333 8.42162 0.99993 8.70907 1 9V18C1 18.5304 1.21071 19.0391 1.58579 19.4142C1.96086 19.7893 2.46957 20 3 20H17C17.5304 20 18.0391 19.7893 18.4142 19.4142C18.7893 19.0391 19 18.5304 19 18V10.646" stroke="#FFFFFF" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <div class="clipboard-status">
                <span id="dd_clipboard-count">0</span>
            </div>
        </div>
    </a>
    <?php
    return ob_get_clean();
});

add_shortcode('dd_mieszkania_clipboard', function () {
    ob_start();
    ?>
    <div id="mieszkania-clipboard">
        <div class="dd_clipboard-title">
            <div class="column">
                <h1>Schowek</h1>
                <p id="dd_clipboard-count">(<span>0</span>)</p>
            </div>
        <button id="clear-clipboard-button" class="clear-clipboard">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M6 6L18.7279 18.7279" stroke="#FFFFFF" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M19.0005 6L6.27257 18.7279" stroke="#FFFFFF" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>Wyczyść schowek
        </button>
        </div>
        <div id="dd_clipboard-list"></div>
    </div>
    <?php
    return ob_get_clean();
});