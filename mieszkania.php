<?php
/*
Plugin Name: Mieszkania
Description: Wtyczka do zarządzania mieszkaniami.
Version: 1.2.5
Author: Digital Dimension
*/

use DD\App\BulkPhotoUploadAction;
use DD\App\BulkWalkaroundUploadAction;
use DD\App\FromXmlUpdater\UpdateFromXmlAction;
use DD\App\XmlImporter\XmlImporterAction;

if (!defined('ABSPATH')) {
    exit;
}

define('DD_PLUGIN_ASSETS', plugin_dir_url(__FILE__) . 'assets/');
define('DD_PLUGIN_LOGS', plugin_dir_path(__FILE__) . 'logs/');

define('DD_PLACEHOLDER_FLAT', esc_url(get_theme_mod('placeholder_flat')) ?: DD_PLUGIN_ASSETS . 'images/placeholder_flat.svg');
define('DD_PLACEHOLDER_STORAGE', esc_url(get_theme_mod('placeholder_storage')) ?: DD_PLUGIN_ASSETS . 'images/placeholder_storage.svg');
define('DD_PLACEHOLDER_PARKING', esc_url(get_theme_mod('placeholder_parking')) ?: DD_PLUGIN_ASSETS . 'images/placeholder_parking.svg');

const DD_PLUGIN_VERSION = '1.2.5';

require_once plugin_dir_path(__FILE__) . 'vendor/autoload.php';

require_once plugin_dir_path(__FILE__) . 'constants.php';
require_once plugin_dir_path(__FILE__) . 'includes/post-type.php';
require_once plugin_dir_path(__FILE__) . 'includes/meta-boxes.php';
require_once plugin_dir_path(__FILE__) . 'includes/shortcode.php';
require_once plugin_dir_path(__FILE__) . 'includes/draw-status.php';
require_once plugin_dir_path(__FILE__) . 'includes/rest-api.php';

add_action('init', function() {
    XmlImporterAction::init();
    UpdateFromXmlAction::init();
    BulkPhotoUploadAction::init();
    BulkWalkaroundUploadAction::init();
});

function dd_enqueue_admin_style(){
    wp_register_style( 'custom_wp_admin_css', plugin_dir_url(__FILE__) . 'assets/css/admin-style.css', [], DD_PLUGIN_VERSION );
    wp_enqueue_style( 'custom_wp_admin_css' , ver: DD_PLUGIN_VERSION);

    wp_enqueue_style('select2', 'https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css');
    wp_enqueue_script('select2', 'https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/js/select2.min.js', ['jquery'], null, true);
}
add_action('admin_enqueue_scripts', 'dd_enqueue_admin_style');

function dd_enqueue_assets() {
    wp_enqueue_style('dd_style', plugin_dir_url(__FILE__) . 'assets/css/style.css', ver: DD_PLUGIN_VERSION);
    wp_enqueue_script('dd_script', plugin_dir_url(__FILE__) . 'assets/js/script.js', ['jquery'], ver: DD_PLUGIN_VERSION);
    wp_enqueue_script('dd_storage', plugin_dir_url(__FILE__) . 'assets/js/local-storage.js', ver: DD_PLUGIN_VERSION);
    wp_localize_script('dd_storage', 'DD_PLACEHOLDER', array(
        'flat' => DD_PLACEHOLDER_FLAT,
        'storage' => DD_PLACEHOLDER_STORAGE,
        'parking' => DD_PLACEHOLDER_PARKING,
    ));
}
add_action('wp_enqueue_scripts', 'dd_enqueue_assets');

add_action('admin_enqueue_scripts', function ($hook) {
   if ($hook !== DD_POST_TYPE . '_page_dd-import-xml') {
        return;
    }
    wp_enqueue_script('xml-importer', plugin_dir_url(__FILE__) . 'assets/js/xml-importer.js', ['jquery'], null, true);
    wp_localize_script('xml-importer', 'xmlImporterAjax', [
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('xml_importer_nonce'),
    ]);
});