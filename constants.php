<?php

use DD\App\Enum\Status;
use DD\App\Enum\Type;

const DD_POST_TYPE = 'mieszkanie';

const DD_FEATURED_PROPERTIES = 'dd_featured_properties';
const DD_SAVE_FEATURED_PROPERTIES = 'dd_save_featured_properties';

const DD_LOCATION = 'location';
const DD_PRICE = 'price';
const DD_PRICE_PER_METER = 'price_per_meter';
const DD_STATUS = 'status';
const DD_PDF_LINK = 'pdf_link';
const DD_FLOOR = 'floor';
const DD_SQUARE = 'square';
const DD_ROOMS = 'rooms';
const DD_BUILDING_NUMBER = 'building_number';
const DD_FACILITIES = 'udogodnienia';
const DD_3D_LINK = 'walkaround_3d';
const DD_IMAGES = 'photos';
const DD_ID = 'id_number';
const DD_TYPE = 'property_type';
const DD_EXTERNAL_ID = 'external-id';
const DD_VERTICAL_SHOT = 'vertical_shot';
const DD_ANGLED_SHOT = 'angled_shot';
const DD_PRICE_HISTORY = 'price_history';


const DD_STATUS_CLASSES = [
    Status::Sprzedane->value => 'hotspot-sprzedane',
    Status::Dostepne->value  => 'hotspot-dostepne',
    Status::Rezerwacja->value => 'hotspot-rezerwacja',
];

const DD_TYPE_MIESZKANIE = Type::Mieszkanie->value;
const DD_TYPE_MIEJSCE_POSTOJOWE_GARAZ = Type::Garaz->value;
const DD_TYPE_MIEJSCE_POSTOJOWE_ZEWNATRZ = Type::ParkingZewnatrz->value;
const DD_TYPE_KOMORKA_LOKATORSKA = Type::KomorkaLokatorska->value;
const DD_TYPE_LOKAL_USLUGOWY = Type::LokalUslugowy->value;

const DD_TYPES = [
    DD_TYPE_MIESZKANIE => 'Mieszkanie',
    DD_TYPE_MIEJSCE_POSTOJOWE_GARAZ => 'Miejsce postojowe - garaż',
    DD_TYPE_MIEJSCE_POSTOJOWE_ZEWNATRZ => 'Miejsce postojowe - na zewnątrz',
    DD_TYPE_KOMORKA_LOKATORSKA => 'Komórka lokatorska',
    DD_TYPE_LOKAL_USLUGOWY => 'Lokal usługowy'
];