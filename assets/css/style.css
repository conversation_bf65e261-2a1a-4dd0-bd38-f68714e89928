.mieszkanie.fade-in {
  opacity: 0;
  animation: fadeIn 0.3s forwards;
}
.mieszkanie.fade-out {
  opacity: 1;
  animation: fadeOut 0.5s forwards;
  pointer-events: none;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}
@keyframes fadeOut {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
.leaflet-rrose-close-button,
.da-address-wrapper {
  display: none;
}

.layout-tooltip .leaflet-container {
  overflow: hidden !important;
}

.leaflet-rrose-content {
  width: 300px !important;
  margin: 10px !important;
}
.leaflet-rrose-content .mieszkanie-tooltip {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 10px;
}
.leaflet-rrose-content .mieszkanie-tooltip .row {
  display: flex;
  justify-content: space-between;
  gap: 10px;
  align-items: center;
}
.leaflet-rrose-content .mieszkanie-tooltip .row:last-of-type .list {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  font-weight: 600;
}
.leaflet-rrose-content .mieszkanie-tooltip .row:last-of-type .list li:first-letter {
  text-transform: capitalize;
}
.leaflet-rrose-content .mieszkanie-tooltip .title:first-letter {
  text-transform: capitalize;
}
.leaflet-rrose-content .button {
  gap: 10px;
  text-transform: uppercase;
  font-size: 1rem;
}
.leaflet-rrose-content .button .arrow {
  max-width: 50px;
  margin: 0;
}

.mieszkanie-single {
  padding-bottom: 7rem;
}
.mieszkanie-single .left-right {
  gap: 40px;
}
.mieszkanie-single .left-right .left {
  display: flex;
  flex-direction: column;
  flex-basis: calc(60% - 15px);
  gap: 30px;
}
.mieszkanie-single .left-right .left .main-image {
  display: flex;
  position: relative;
  height: 100%;
}
.mieszkanie-single .left-right .left .main-image img {
  display: block;
  max-height: 550px;
  max-width: 550px;
  margin: auto;
  object-fit: contain;
  aspect-ratio: 1/1;
  width: 100%;
  padding: 3rem 0;
  transition: opacity 0.3s ease-out, transform 0.3s ease-out;
}
.mieszkanie-single .left-right .left .main-image img.fade-out {
  opacity: 0;
  transform: scale(0.8);
}
.mieszkanie-single .left-right .left .main-image img.placeholder {
  display: block;
  margin: auto;
  max-width: 250px;
}
.mieszkanie-single .left-right .left .main-image .placeholder-text {
  display: flex;
  justify-content: center;
  position: absolute;
  width: 100%;
  bottom: 25%;
  font-weight: 700;
  text-align: center;
  text-transform: uppercase;
}
.mieszkanie-single .left-right .left .gallery {
  display: flex;
  gap: 30px;
}
.mieszkanie-single .left-right .left .gallery .walkaround,
.mieszkanie-single .left-right .left .gallery .thumbnail {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  flex: 1 1 0;
  cursor: pointer;
  border-radius: 4px;
  gap: 20px;
}
.mieszkanie-single .left-right .left .gallery .walkaround .title,
.mieszkanie-single .left-right .left .gallery .thumbnail .title {
  font-weight: 700;
  font-size: 1rem;
  text-align: center;
  text-transform: uppercase;
}
.mieszkanie-single .left-right .left .gallery .thumbnail img {
  max-width: 100px;
  object-fit: contain;
  aspect-ratio: 1/1;
}
.mieszkanie-single .left-right .left .gallery .walkaround.empty {
  justify-content: center;
  cursor: unset;
}
.mieszkanie-single .left-right .left .gallery .walkaround img {
  width: 100px;
}
.mieszkanie-single .left-right .right {
  flex: 1;
}
.mieszkanie-single .left-right .right .info {
  padding: 20px;
  font-weight: 400;
}
.mieszkanie-single .left-right .right .info .row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  padding: 2rem 0;
}
.mieszkanie-single .left-right .right .info .row .column {
  display: flex;
  flex-direction: column;
  text-align: right;
}
.mieszkanie-single .left-right .right .info .row:after {
  content: "";
  position: absolute;
  width: 100%;
  height: 0.5px;
  background: #897f7b;
  top: 100%;
  left: 0;
}
.mieszkanie-single .left-right .right .info .row:last-of-type strong {
  text-transform: capitalize;
  max-width: 151px;
}
.mieszkanie-single .left-right .right .wrapper {
  display: flex;
  gap: 20px;
  align-items: center;
}
.mieszkanie-single .left-right .right .wrapper .link-pdf,
.mieszkanie-single .left-right .right .wrapper #show-price-history {
  flex-basis: calc(50% - 5px);
  margin-top: 1.5rem;
  white-space: nowrap;
  font-size: 1rem;
}
.mieszkanie-single .left-right .right .wrapper #show-price-history {
  padding: 13px 10px;
}
.mieszkanie-single .left-right .right .link-pdf {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  width: 100%;
  padding: 10px 15px;
  text-decoration: none;
  margin-top: 1.5rem;
  border-radius: 4px;
  font-weight: 700;
  font-size: 1rem;
  text-transform: uppercase;
  text-align: center;
}
.mieszkanie-single .left-right .right .dd_add-to-clipboard {
  position: relative;
  right: 0;
}

#mieszkania-clipboard .dd_clipboard-title {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
}
#mieszkania-clipboard .dd_clipboard-title .column {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
}
#mieszkania-clipboard .dd_clipboard-title button {
  background: none;
  border: none;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  cursor: pointer;
}
#mieszkania-clipboard #dd_clipboard-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  padding: 3rem 0;
  gap: 20px;
}
#mieszkania-clipboard #dd_clipboard-list .mieszkanie {
  margin-bottom: 1rem;
  padding: 3rem 1rem 1rem 1rem;
}
#mieszkania-clipboard #dd_clipboard-list .mieszkanie button {
  bottom: 15px;
}

.dd_clipboard-button {
  position: relative;
  width: 45px;
  height: 45px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: 0.2s ease-in-out;
}
.dd_clipboard-button:hover, .dd_clipboard-button:focus, .dd_clipboard-button:active {
  transition: 0.2s ease-in-out;
}
.dd_clipboard-button .clipboard-status {
  position: absolute;
  top: 6px;
  right: 4px;
  font-size: 14px;
  font-weight: 800;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.mieszkania-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}
.mieszkania-list .mieszkanie {
  margin-bottom: 3rem;
}

.home .mieszkaniaSwiper .swiper-wrapper .mieszkanie {
  margin-bottom: 3rem;
}

.mieszkaniaSwiper .swiper-wrapper {
  padding: 5rem 0 3rem 0;
  gap: 10px;
}
.mieszkaniaSwiper .swiper-wrapper .mieszkanie {
  padding: 1.25rem;
}
.mieszkaniaSwiper .swiper-wrapper .mieszkanie .content .top ul,
.mieszkaniaSwiper .swiper-wrapper .mieszkanie .content .top ol,
.mieszkaniaSwiper .swiper-wrapper .mieszkanie .content .top li {
  padding-inline-start: 0 !important;
}
.mieszkaniaSwiper .swiper-wrapper .mieszkanie .dd_add-to-clipboard {
  bottom: 1.5rem !important;
}

.mieszkaniaSwiper .mieszkanie,
#dd_clipboard-list .mieszkanie,
.mieszkania-list .mieszkanie {
  display: flex;
  position: relative;
  flex-basis: calc(25% - 15px);
  height: auto;
}
.mieszkaniaSwiper .mieszkanie .property-link,
#dd_clipboard-list .mieszkanie .property-link,
.mieszkania-list .mieszkanie .property-link {
  display: flex;
  flex-direction: column;
  width: 100%;
  color: #4d4c46;
}
.mieszkaniaSwiper .mieszkanie .property-link img,
#dd_clipboard-list .mieszkanie .property-link img,
.mieszkania-list .mieszkanie .property-link img {
  display: block;
  margin: auto;
  padding-bottom: 1rem;
  height: 210px;
}
.mieszkaniaSwiper .mieszkanie .property-link img.placeholder,
#dd_clipboard-list .mieszkanie .property-link img.placeholder,
.mieszkania-list .mieszkanie .property-link img.placeholder {
  width: 35%;
  min-height: 200px;
  object-fit: contain;
  aspect-ratio: 1/1;
}
.mieszkaniaSwiper .mieszkanie .property-linka:hover,
#dd_clipboard-list .mieszkanie .property-linka:hover,
.mieszkania-list .mieszkanie .property-linka:hover {
  color: unset;
}
.mieszkaniaSwiper .mieszkanie .property-link .title,
#dd_clipboard-list .mieszkanie .property-link .title,
.mieszkania-list .mieszkanie .property-link .title {
  font-weight: 700;
  line-height: 2.5rem;
}
.mieszkaniaSwiper .mieszkanie .property-link .content,
#dd_clipboard-list .mieszkanie .property-link .content,
.mieszkania-list .mieszkanie .property-link .content {
  display: flex;
  flex-direction: column;
  gap: 10px;
  height: 100%;
}
.mieszkaniaSwiper .mieszkanie .property-link .content.with-rooms,
#dd_clipboard-list .mieszkanie .property-link .content.with-rooms,
.mieszkania-list .mieszkanie .property-link .content.with-rooms {
  min-height: 125px;
}
.mieszkaniaSwiper .mieszkanie .property-link .content .top,
#dd_clipboard-list .mieszkanie .property-link .content .top,
.mieszkania-list .mieszkanie .property-link .content .top {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.mieszkaniaSwiper .mieszkanie .property-link .content .top li,
#dd_clipboard-list .mieszkanie .property-link .content .top li,
.mieszkania-list .mieszkanie .property-link .content .top li {
  padding-left: 0;
}
.mieszkaniaSwiper .mieszkanie .property-link .content .top .info,
#dd_clipboard-list .mieszkanie .property-link .content .top .info,
.mieszkania-list .mieszkanie .property-link .content .top .info {
  display: flex;
  flex-direction: column;
  align-items: start;
  flex-wrap: wrap;
  gap: 5px;
  max-width: 60%;
}
.mieszkaniaSwiper .mieszkanie .property-link .content .top .info div,
#dd_clipboard-list .mieszkanie .property-link .content .top .info div,
.mieszkania-list .mieszkanie .property-link .content .top .info div {
  display: flex;
  align-items: start;
  gap: 7px;
}
.mieszkaniaSwiper .mieszkanie .property-link .content .top .status,
#dd_clipboard-list .mieszkanie .property-link .content .top .status,
.mieszkania-list .mieszkanie .property-link .content .top .status {
  font-weight: 700;
  font-size: 1rem;
  white-space: nowrap;
}
.mieszkaniaSwiper .mieszkanie .property-link .bottom,
#dd_clipboard-list .mieszkanie .property-link .bottom,
.mieszkania-list .mieszkanie .property-link .bottom {
  display: flex;
  margin-top: auto;
}
.mieszkaniaSwiper .mieszkanie .property-link .bottom .price,
#dd_clipboard-list .mieszkanie .property-link .bottom .price,
.mieszkania-list .mieszkanie .property-link .bottom .price {
  display: flex;
  flex-direction: column;
  gap: 5px;
  width: 75%;
}
.mieszkaniaSwiper .mieszkanie .property-link .bottom .price .price-value,
#dd_clipboard-list .mieszkanie .property-link .bottom .price .price-value,
.mieszkania-list .mieszkanie .property-link .bottom .price .price-value {
  font-weight: 700;
  font-size: 1.5rem;
  line-height: 25px;
}

.dd_add-to-clipboard,
.dd_remove-from-clipboard {
  position: absolute;
  bottom: 0px;
  right: 15px;
  width: 45px;
  height: 45px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease-in-out;
}
.dd_add-to-clipboard.added,
.dd_remove-from-clipboard.added {
  transition: 0.2s ease-in-out;
  background: #4caf50 !important;
}

.swiper-button-next,
.swiper-button-prev {
  background-color: transparent;
  border: none;
  top: 25px !important;
  position: absolute;
  visibility: hidden;
  width: fit-content !important;
}
.swiper-button-next:after,
.swiper-button-prev:after {
  padding: 5px;
  display: block;
}

.swiper-button-prev::after {
  background-image: url(../images/arrow-left.svg);
  background-repeat: no-repeat;
  width: 35px;
  height: 35px;
  content: "" !important;
}

.swiper-button-next::after {
  background-image: url(../images/arrow-right.svg);
  background-repeat: no-repeat;
  width: 35px;
  height: 35px;
  content: "" !important;
}

.fade-in {
  animation: fadeOut 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
.status.sprzedane {
  color: #c53521;
}
.status.dostepne {
  color: #38894a;
}
.status.rezerwacja {
  color: #d79729;
}

.hotspot-dostepne {
  fill: #38894a;
  stroke-width: 1;
  fill-opacity: 0.41;
  stroke: #ffffff;
  stroke-opacity: 0.81;
  transition: 0.2s ease-in-out;
}

.hotspot-sprzedane {
  fill: #c53521;
  stroke-width: 1;
  fill-opacity: 0.41;
  stroke: #ffffff;
  stroke-opacity: 0.81;
  transition: 0.2s ease-in-out;
}

.hotspot-rezerwacja {
  fill: #d79729;
  stroke-width: 1;
  fill-opacity: 0.41;
  stroke: #ffffff;
  stroke-opacity: 0.81;
  transition: 0.2s ease-in-out;
}

.hotspot-dostepne:hover,
.hotspot-sprzedane:hover,
.hotspot-zarezerwowane:hover {
  fill: #ffffff;
  fill-opacity: 0.81;
  outline: none;
  stroke: #ffffff;
  stroke-opacity: 0.81;
  transition: 0.2s ease-in-out;
}

#main-content .button.reverse.back {
  gap: 10px;
  width: fit-content;
}

.dd-price-history-modal-overlay {
  position: fixed;
  inset: 0;
  z-index: 1201;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}
.dd-price-history-modal-overlay.is-open {
  opacity: 1;
  visibility: visible;
}
.dd-price-history-modal-overlay.is-open .dd-price-history-modal {
  transform: translateY(0);
  opacity: 1;
}
.dd-price-history-modal-overlay .dd-price-history-modal {
  padding: 24px;
  max-width: 65%;
  width: 65%;
  position: relative;
  overflow-y: visible;
  max-height: none;
  transform: translateY(20px);
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.3s ease;
}
.dd-price-history-modal-overlay .dd-price-history-modal .table-wrapper table {
  width: 100%;
  border-collapse: collapse;
  font-size: 1.125rem;
}
.dd-price-history-modal-overlay .dd-price-history-modal .table-wrapper table thead th,
.dd-price-history-modal-overlay .dd-price-history-modal .table-wrapper table tbody td {
  padding: 20px 8px;
}
.dd-price-history-modal-overlay .dd-price-history-modal .table-wrapper table thead th:first-of-type,
.dd-price-history-modal-overlay .dd-price-history-modal .table-wrapper table tbody td:first-of-type {
  text-align: left;
  padding: 20px 20px 20px 20px;
}
.dd-price-history-modal-overlay .dd-price-history-modal .table-wrapper table thead th {
  font-weight: 700;
  border-bottom: 1px solid #9e9e9e;
  text-align: center;
  color: #214128;
}
.dd-price-history-modal-overlay .dd-price-history-modal .table-wrapper table tbody:before {
  content: "@";
  display: block;
  line-height: 10px;
  text-indent: -99999px;
}
.dd-price-history-modal-overlay .dd-price-history-modal .table-wrapper table tbody tr:first-of-type {
  font-weight: 700;
}
.dd-price-history-modal-overlay .dd-price-history-modal .table-wrapper table tbody td {
  text-align: center;
}
.dd-price-history-modal-overlay .dd-price-history-modal .table-wrapper table tbody td:nth-child(odd) {
  color: #214128;
}
.dd-price-history-modal-overlay .dd-price-history-modal .dd-price-history-modal-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: transparent;
  border: none;
  cursor: pointer;
  width: 30px;
  height: 30px;
  padding: 0;
}
.dd-price-history-modal-overlay .dd-price-history-modal .dd-price-history-modal-close svg {
  width: 100%;
  height: 100%;
}

@media (max-width: 991.98px) {
  .mieszkanie-single {
    padding: 5rem 0;
  }
  .mieszkanie-single .left-right {
    flex-direction: column;
    flex-wrap: nowrap;
  }
  .mieszkanie-single .left-right .left {
    flex-basis: 100%;
  }
  .mieszkanie-single .left-right .left .main-image {
    min-height: unset;
    flex-direction: column;
    gap: 10px;
  }
  .mieszkanie-single .left-right .left .main-image img {
    max-width: 100%;
    max-height: 400px;
    padding: 0;
  }
  .mieszkanie-single .left-right .left .main-image img.placeholder {
    max-width: 200px;
  }
  .mieszkanie-single .left-right .left .main-image .placeholder-text {
    position: relative;
  }
  .mieszkanie-single .left-right .left .gallery {
    gap: 15px;
    white-space: nowrap;
    overflow-x: auto;
    overflow-y: hidden;
  }
  .mieszkanie-single .left-right .left .gallery .walkaround,
  .mieszkanie-single .left-right .left .gallery .thumbnail {
    padding: 1rem;
    min-width: 42%;
    gap: 10px;
  }
  .mieszkanie-single .left-right .left .gallery .walkaround .title,
  .mieszkanie-single .left-right .left .gallery .thumbnail .title {
    white-space: pre-wrap;
    text-align: center;
    font-size: 0.875rem;
    word-break: break-word;
  }
  .mieszkanie-single .left-right .left .gallery .walkaround img,
  .mieszkanie-single .left-right .left .gallery .thumbnail img {
    white-space: nowrap;
    max-width: 60px;
  }
  .mieszkanie-single .left-right .left .gallery .walkaround.empty .title {
    max-width: 160px;
    font-size: 12px;
  }
  .mieszkanie-single .left-right .right .info {
    padding: 0;
  }
  .mieszkanie-single .left-right .right .wrapper {
    flex-wrap: wrap;
  }
  .mieszkanie-single .left-right .right .wrapper #show-price-history {
    margin: 0;
  }
  .mieszkanie-single .left-right .right .wrapper .link-pdf,
  .mieszkanie-single .left-right .right .wrapper #show-price-history {
    flex-basis: 100%;
  }
  .mieszkanie-single .left-right .right .link-pdf {
    margin-top: 20px;
  }
  .mieszkaniaSwiper {
    overflow: hidden;
  }
  .mieszkaniaSwiper .swiper-wrapper {
    padding: 3rem 0;
  }
  .mieszkaniaSwiper .swiper-wrapper .mieszkanie {
    margin-top: 3rem;
    padding: 0.5rem;
  }
  .mieszkaniaSwiper .swiper-button-prev,
  .mieszkaniaSwiper .swiper-button-next {
    visibility: visible;
  }
  .mieszkaniaSwiper .swiper-button-prev {
    left: var(--swiper-navigation-sides-offset, 40%) !important;
  }
  .mieszkaniaSwiper .swiper-button-next {
    right: var(--swiper-navigation-sides-offset, 40%) !important;
  }
  #mieszkania-clipboard #dd_clipboard-list .mieszkanie {
    padding: 1rem;
    margin-bottom: unset;
  }
  #dd_clipboard-list,
  .mieszkania-list,
  .mieszkaniaSwiper {
    padding: 3rem 0;
  }
  #dd_clipboard-list .mieszkanie,
  .mieszkania-list .mieszkanie,
  .mieszkaniaSwiper .mieszkanie {
    flex-basis: calc(50% - 10px);
  }
  .dd-price-history-modal-overlay.is-open .dd-price-history-modal {
    transform: translateY(50px);
  }
  .dd-price-history-modal-overlay .dd-price-history-modal {
    max-width: 100%;
    width: 100%;
    overflow-y: auto;
  }
  .dd-price-history-modal-overlay .dd-price-history-modal .modal-title {
    padding-left: 0;
  }
  .dd-price-history-modal-overlay .dd-price-history-modal .table-wrapper {
    overflow: auto;
    max-height: 90vh;
    margin-top: 1rem;
    padding-bottom: 0;
  }
  .dd-price-history-modal-overlay .dd-price-history-modal .table-wrapper table thead th,
  .dd-price-history-modal-overlay .dd-price-history-modal .table-wrapper table tbody td {
    padding: 20px;
    white-space: nowrap;
  }
  .dd-price-history-modal-overlay .dd-price-history-modal .table-wrapper table thead th:first-of-type,
  .dd-price-history-modal-overlay .dd-price-history-modal .table-wrapper table tbody td:first-of-type {
    padding: 20px 20px 20px 0px;
  }
  .dd-price-history-modal-overlay .dd-price-history-modal .dd-price-history-modal-close {
    width: 25px;
    height: 25px;
  }
}
@media (max-width: 767.98px) {
  .leaflet-rrose {
    transform: translate3d(0%, 25px, 0px) !important;
  }
  .mieszkanie-single {
    padding: 3rem 0;
  }
  #dd_clipboard-list .mieszkanie,
  .mieszkania-list .mieszkanie,
  .mieszkaniaSwiper .mieszkanie {
    flex-basis: 100%;
  }
  .mieszkania-list .mieszkanie {
    margin-bottom: 1.5rem;
  }
  .mieszkaniaSwiper .swiper-wrapper {
    padding-bottom: 0;
  }
  .mieszkaniaSwiper .swiper-button-prev {
    left: var(--swiper-navigation-sides-offset, 35%) !important;
  }
  .mieszkaniaSwiper .swiper-button-next {
    right: var(--swiper-navigation-sides-offset, 35%) !important;
  }
}
@media (max-width: 320px) {
  .leaflet-rrose {
    transform: translate3d(25%, 0px, 0px) !important;
  }
  .leaflet-rrose .leaflet-rrose-content {
    max-width: fit-content !important;
    width: fit-content !important;
  }
  .leaflet-rrose .leaflet-rrose-content .mieszkanie-tooltip .row {
    flex-wrap: wrap;
  }
  .mieszkaniaSwiper .swiper-button-prev {
    left: var(--swiper-navigation-sides-offset, 25%) !important;
  }
  .mieszkaniaSwiper .swiper-button-next {
    right: var(--swiper-navigation-sides-offset, 25%) !important;
  }
}
.dd-3d-tour-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: none;
  z-index: 9999;
  backdrop-filter: blur(5px);
  align-items: center;
  justify-content: center;
}

.dd-3d-tour-modal-overlay.is-open {
  display: flex;
}

.dd-3d-tour-modal {
  background: white;
  width: 90%;
  height: 90vh;
  border-radius: 12px;
  position: relative;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.dd-3d-tour-modal-close svg {
  position: absolute;
  top: 15px;
  right: 15px;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.dd-3d-tour-modal-close:hover {
  background: #e0e0e0;
}

.dd-3d-tour-modal-iframe {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: 12px;
}

/*# sourceMappingURL=style.css.map */
